.frame {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: var(--semi-color-bg-0);
  width: 1440px;
  overflow: hidden;
  font-family: Inter;
  font-weight: 600;

  .nav {
    flex-shrink: 0;
    align-self: stretch;
    height: 60px;

    .semiIconsSemiLogo {
      color: var(--semi-color-text-0);
      font-size: 36px;
    }

    .dIv {
      display: inline-flex;
      align-items: center;
      justify-content: flex-start;
      column-gap: 16px;
      color: var(--semi-color-text-2);
      font-size: 20px;

      .semiIconsFeishuLogo {
        color: var(--semi-color-text-2);
        font-size: 20px;
      }

      .avatar {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
      }
    }
  }

  .main {
    display: flex;
    flex-shrink: 0;
    align-items: flex-start;
    align-self: stretch;

    .left {
      flex-shrink: 0;
      width: 240px;
      height: 982px;

      .navItem {
        width: 223px;

        .iconIntro {
          width: 20px;
          height: 20px;
        }
      }
    }

    .right {
      display: flex;
      flex-basis: 0;
      flex-direction: column;
      flex-grow: 1;
      align-items: flex-start;
      padding-top: 40px;
      padding-left: 40px;
      row-gap: 24px;

      .item {
        flex-shrink: 0;
        min-width: 90px;
        line-height: 28px;
        letter-spacing: -0.6px;
        color: var(--semi-color-text-0);
        font-size: 20px;
      }

      .frame1321317607 {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        border-radius: 12px;
        background: var(--semi-color-fill-0);
        padding: 220px 499px;
        height: 20px;
        overflow: hidden;
        line-height: 20px;
        letter-spacing: -0.14px;
        color: var(--semi-color-disabled-text);
        font-size: 14px;

        .yourContentHere {
          min-width: 123px;
        }
      }
    }
  }
}
